package com.qbook.insight.handler.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qbook.insight.common.constant.MsgTopic;
import com.qbook.insight.common.constant.RMsg;
import com.qbook.insight.common.constant.ReportStage;
import com.qbook.insight.common.constant.Role;
import com.qbook.insight.common.entity.Client;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.exception.BizException;
import com.qbook.insight.common.mapper.ClientMapper;
import com.qbook.insight.common.mapper.ReportMapper;
import com.qbook.insight.common.util.RedisUtil;
import com.qbook.insight.common.vo.PageParam;
import com.qbook.insight.common.vo.ReportVO;
import com.qbook.insight.handler.service.MemberService;
import com.qbook.insight.handler.service.ReportService;
import com.qbook.insight.handler.service.UserService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 报告操作实现
 *
 * <AUTHOR>
 */
@Service
public class ReportServiceImpl implements ReportService {

  @Resource private ReportMapper reportMapper;
  @Resource private ClientMapper clientMapper;
  @Resource private UserService userService;
  @Resource private RedisUtil redisUtil;
  @Resource private MemberService memberService;

  /** 根据客户ID获取Client实体 */
  private Client getById(long id) {
    return clientMapper.selectById(id);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public int add(Report report) {
    Client client = getById(report.getClientId());
    if (client == null) {
      throw new BizException(RMsg.ERR_PARAM);
    }
    if (!client.getUserId().equals(userService.getUserId())) {
      throw new BizException(RMsg.ERR_PARAM);
    }
    memberService.consumeReport();
    report.setUserId(client.getUserId());
    report.setCorpId(client.getCorpId());
    report.setStage(ReportStage.CREATE);
    int ret = reportMapper.insert(report);
    if (ret <= 0) {
      return 0;
    }

    redisUtil.rpush(MsgTopic.TASK_COLLECTOR, JSONUtil.toJsonStr(report));
    return 1;
  }

  @Override
  public int delete(long id) {
    LambdaUpdateWrapper<Report> wrapper = new LambdaUpdateWrapper<>();
    wrapper.eq(Report::getId, id);
    wrapper.eq(Report::getUserId, userService.getUserId());
    return reportMapper.delete(wrapper);
  }

  @Override
  public int update(Long id, Report report) {
    if (id == null || report == null) {
      throw new BizException(RMsg.ERR_PARAM);
    }

    report.setId(id);
    Report currentReport = reportMapper.selectById(id);
    if (currentReport == null) {
      throw new BizException("该报告不存在");
    }

    int summaryAudit =
        report.getSummaryAuditTag() != null
            ? report.getSummaryAuditTag()
            : currentReport.getSummaryAuditTag();
    int detailAudit =
        report.getDetailAuditTag() != null
            ? report.getDetailAuditTag()
            : currentReport.getDetailAuditTag();
    if (summaryAudit == 1 && detailAudit == 1) {
      report.setStage(ReportStage.AUDITED);
    }
    int ret = reportMapper.updateById(report);
    if (ret <= 0) {
      return 0;
    }

    redisUtil.rpush(MsgTopic.TASK_GENERATOR, JSONUtil.toJsonStr(currentReport));
    return 1;
  }

  @Override
  public List<Report> list() {
    return reportMapper.selectList(null);
  }

  @Override
  public IPage<ReportVO> page(PageParam pageParam, String realName, String corpName, String taxId) {
    IPage<ReportVO> page = new Page<>(pageParam.getPage(), pageParam.getPageSize());
    ReportVO reportVO = new ReportVO();
    reportVO.setRealName(realName);
    reportVO.setCorpName(corpName);
    reportVO.setTaxId(taxId);

    // 普通用户只能看自己的报告
    if (!userService.hasAnyRoles(Role.ADMIN, Role.AUDITOR)) {
      reportVO.setUserId(userService.getUserId());
    }

    List<ReportVO> records = reportMapper.selectReportVO(page, reportVO);
    page.setRecords(records);
    return page;
  }

  @Override
  public List<Report> selectByClientIds(List<Long> idList) {
    return reportMapper.selectByClientIds(idList);
  }

  @Override
  public void deleteByClientId(Long clientId) {
    LambdaUpdateWrapper<Report> wrapper = new LambdaUpdateWrapper<>();
    wrapper.eq(Report::getUserId, userService.getUserId());
    wrapper.eq(Report::getClientId, clientId);
    reportMapper.delete(wrapper);
  }
}
