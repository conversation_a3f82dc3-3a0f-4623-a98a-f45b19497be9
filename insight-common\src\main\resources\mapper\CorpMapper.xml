<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qbook.insight.common.mapper.CorpMapper">

  <select id="selectCorpInfoById"
    resultType="com.qbook.insight.common.vo.CorpInfoVO">
    select
    c.*,cd.cap_type_name,cd.op_scope,cd.frname,cd.reg_org_name,
    cd.credit_rating,cd.is_export_company,cd.is_high_tech,cd.is_small_micro,
    cd.fr_id_card,cd.fr_id_type,cd.fr_origin,cd.taxpayer_type
    from corp c left join corp_detail cd on c.id = cd.corp_id
    where c.id = #{id}
  </select>
</mapper>
