package com.qbook.insight.analyzer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qbook.insight.analyzer.service.AnaResultService;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.mapper.IndicatorConfigMapper;
import com.qbook.insight.common.util.IndicatorCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 报告结果分析实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AnaResultServiceImpl implements AnaResultService {

  @Resource private IndicatorConfigMapper indicatorConfigMapper;
  @Resource private IndicatorCalculator indicatorCalculator;

  @Override
  public void anaReportResult(Report report) {
    LambdaQueryWrapper<IndicatorConfig> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(IndicatorConfig::getStatus, 1);
    List<IndicatorConfig> configList = indicatorConfigMapper.selectList(wrapper);

    Map<String, Object> map = new HashMap<>();
    map.put("tax_id", "91330681MA2D7C9R1B");
    map.put("year", "2024");

    Map<String, Object> resultMap =
        configList.stream()
            .map(config -> indicatorCalculator.calculate(config, map))
            .flatMap(m -> m.entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    log.info("报告结果:{}", resultMap);
  }
}
