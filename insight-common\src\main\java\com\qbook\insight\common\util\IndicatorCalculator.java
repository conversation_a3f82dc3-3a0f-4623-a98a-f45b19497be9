package com.qbook.insight.common.util;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qbook.insight.common.constant.DsName;
import com.qbook.insight.common.constant.IndicatorType;
import com.qbook.insight.common.entity.IndicatorConfig;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * 指标计算通用类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IndicatorCalculator {

  private final JdbcTemplate jdbcTemplate;
  private static final Pattern PARAM_PATTERN = Pattern.compile("\\{\\s*([\\w-]+)\\s*}");

  /**
   * 计算指标值
   *
   * @param config 指标计算配置
   * @param queryParams 条件参数Map
   * @return 指标值
   */
  @DS(DsName.DATA)
  public Map<String, Object> calculate(IndicatorConfig config, Map<String, Object> queryParams) {

    String sql = buildSql(config, queryParams);
    Map<String, Object> resultMap = new HashMap<>();

    try {
      if (IndicatorType.BIGDECIMAL.equals(config.getReturnType())) {
        BigDecimal result = jdbcTemplate.queryForObject(sql, BigDecimal.class);
        resultMap.put(config.getIndicatorCode(), result);
      } else if (IndicatorType.OBJECT.equals(config.getReturnType())) {
        Map<String, Object> result = jdbcTemplate.queryForMap(sql);
        // 处理YEAR()函数返回Date类型的问题
        result = processYearColumns(result);
        resultMap.put(config.getIndicatorCode(), result);
      } else if (IndicatorType.LIST.equals(config.getReturnType())) {
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
        // 处理YEAR()函数返回Date类型的问题
        result = result.stream()
            .map(this::processYearColumns)
            .collect(java.util.stream.Collectors.toList());
        resultMap.put(config.getIndicatorCode(), result);
      }
      log.debug("计算指标[{}]:执行SQL[{}],执行结果[{}]", config.getIndicatorCode(), sql, resultMap);
      return resultMap;
    } catch (Exception e) {
      log.error("指标计算失败 - 指标: {}, SQL: {}", config.getIndicatorCode(), sql, e);
      throw new RuntimeException("指标计算失败: " + e.getMessage(), e);
    }
  }

  /** 构建完整SQL */
  private String buildSql(IndicatorConfig config, Map<String, Object> params) {
    return processCondition(config.getExecuteSql(), params);
  }

  /** 处理条件中的参数占位符 */
  private String processCondition(String condition, Map<String, Object> params) {
    if (condition == null || condition.isEmpty()) {
      return null;
    }
    if (params == null) {
      return condition;
    }

    StringBuffer result = new StringBuffer();
    Matcher matcher = PARAM_PATTERN.matcher(condition);
    while (matcher.find()) {
      String paramName = matcher.group(1);
      Object value = params.get(paramName);
      if (value == null) {
        throw new IllegalArgumentException("缺少必要参数: " + paramName);
      }
      String replacement =
          (value instanceof String)
              ? "'" + value.toString().replace("'", "''") + "'"
              : value.toString();
      matcher.appendReplacement(result, replacement);
    }
    matcher.appendTail(result);
    return result.toString();
  }

  /**
   * 处理YEAR()函数返回Date类型的问题
   * MySQL JDBC驱动有时会将YEAR()函数的结果映射为Date类型而不是Integer
   */
  private Map<String, Object> processYearColumns(Map<String, Object> row) {
    if (row == null) {
      return row;
    }

    Map<String, Object> processedRow = new HashMap<>(row);
    for (Map.Entry<String, Object> entry : row.entrySet()) {
      String key = entry.getKey();
      Object value = entry.getValue();

      // 如果字段名为year且值是Date类型，转换为年份数字
      if ("year".equals(key) && value instanceof java.util.Date) {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.setTime((java.util.Date) value);
        processedRow.put(key, cal.get(java.util.Calendar.YEAR));
      }
    }
    return processedRow;
  }
}
